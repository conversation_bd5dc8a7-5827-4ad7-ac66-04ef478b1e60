#!/usr/bin/env python3
"""
Free AugmentCode GUI 启动脚本
用于启动图形用户界面版本的 Free AugmentCode 工具
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import customtkinter
        import PIL
        return True
    except ImportError as e:
        print(f"缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter", "pillow"])
        print("依赖安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 Free AugmentCode GUI 启动器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 10):
        print("❌ 错误: 需要 Python 3.10 或更高版本")
        print(f"当前版本: {sys.version}")
        input("按回车键退出...")
        return
    
    print(f"✅ Python 版本: {sys.version.split()[0]}")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 缺少必要依赖")
        choice = input("是否自动安装依赖? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_dependencies():
                input("按回车键退出...")
                return
        else:
            print("请手动安装依赖: pip install customtkinter pillow")
            input("按回车键退出...")
            return
    
    print("✅ 依赖检查通过")
    
    # 启动GUI
    try:
        print("🚀 启动 GUI...")
        from gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ GUI 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
