import os
import json
import sqlite3
import re
from typing import Dict, List, Optional
from datetime import datetime
from .paths import get_storage_path, get_db_path, get_workspace_storage_path


def get_vscode_accounts() -> Dict:
    """
    获取VS Code中的账号信息
    
    Returns:
        dict: 包含账号信息的字典
        {
            'github_accounts': list,
            'microsoft_accounts': list,
            'current_user': str,
            'last_login': str
        }
    """
    result = {
        'github_accounts': [],
        'microsoft_accounts': [],
        'current_user': '',
        'last_login': '',
        'session_info': {}
    }
    
    try:
        storage_path = get_storage_path()
        if not os.path.exists(storage_path):
            return result
        
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        # 查找GitHub账号信息
        github_keys = [key for key in storage_data.keys() if 'github' in key.lower()]
        for key in github_keys:
            if 'user' in key.lower() or 'account' in key.lower():
                value = storage_data[key]
                if isinstance(value, str) and '@' in value:
                    result['github_accounts'].append({
                        'email': value,
                        'source': key,
                        'type': 'GitHub'
                    })
                elif isinstance(value, dict):
                    if 'login' in value:
                        result['github_accounts'].append({
                            'username': value.get('login', ''),
                            'email': value.get('email', ''),
                            'name': value.get('name', ''),
                            'source': key,
                            'type': 'GitHub'
                        })
        
        # 查找Microsoft账号信息
        ms_keys = [key for key in storage_data.keys() if any(term in key.lower() for term in ['microsoft', 'azure', 'outlook', 'live'])]
        for key in ms_keys:
            value = storage_data[key]
            if isinstance(value, str) and '@' in value:
                result['microsoft_accounts'].append({
                    'email': value,
                    'source': key,
                    'type': 'Microsoft'
                })
            elif isinstance(value, dict):
                if 'username' in value or 'email' in value:
                    result['microsoft_accounts'].append({
                        'username': value.get('username', ''),
                        'email': value.get('email', ''),
                        'name': value.get('displayName', ''),
                        'source': key,
                        'type': 'Microsoft'
                    })
        
        # 查找当前用户信息
        user_keys = [key for key in storage_data.keys() if 'currentuser' in key.lower() or 'activeuser' in key.lower()]
        for key in user_keys:
            value = storage_data[key]
            if isinstance(value, str):
                result['current_user'] = value
                break
        
        # 查找会话信息
        session_keys = [key for key in storage_data.keys() if 'session' in key.lower() or 'token' in key.lower()]
        for key in session_keys:
            if len(key) < 100:  # 避免处理过长的key
                result['session_info'][key] = str(storage_data[key])[:100] + '...' if len(str(storage_data[key])) > 100 else storage_data[key]
        
    except (json.JSONDecodeError, OSError, PermissionError) as e:
        result['error'] = str(e)
    
    return result


def get_augment_account_info() -> Dict:
    """
    获取AugmentCode扩展的账号信息
    
    Returns:
        dict: 包含AugmentCode账号信息的字典
    """
    result = {
        'logged_in': False,
        'email': '',
        'username': '',
        'user_id': '',
        'subscription_info': {},
        'login_time': '',
        'token_info': {},
        'usage_stats': {}
    }
    
    try:
        # 从storage.json中查找AugmentCode相关信息
        storage_path = get_storage_path()
        if os.path.exists(storage_path):
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            
            # 查找Augment相关的键
            augment_keys = [key for key in storage_data.keys() if 'augment' in key.lower()]
            
            for key in augment_keys:
                value = storage_data[key]
                
                # 查找用户信息
                if 'user' in key.lower():
                    if isinstance(value, dict):
                        result['email'] = value.get('email', '')
                        result['username'] = value.get('username', value.get('name', ''))
                        result['user_id'] = value.get('id', value.get('userId', ''))
                        if result['email'] or result['username']:
                            result['logged_in'] = True
                    elif isinstance(value, str) and '@' in value:
                        result['email'] = value
                        result['logged_in'] = True
                
                # 查找认证信息
                elif 'auth' in key.lower() or 'token' in key.lower():
                    if isinstance(value, dict):
                        result['token_info'][key] = {k: str(v)[:50] + '...' if len(str(v)) > 50 else v for k, v in value.items()}
                    elif isinstance(value, str) and len(value) > 10:
                        result['token_info'][key] = value[:50] + '...'
                        result['logged_in'] = True
                
                # 查找订阅信息
                elif 'subscription' in key.lower() or 'plan' in key.lower():
                    result['subscription_info'][key] = value
                
                # 查找使用统计
                elif 'usage' in key.lower() or 'stats' in key.lower():
                    result['usage_stats'][key] = value
        
        # 从数据库中查找更多信息
        db_path = get_db_path()
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 查询包含augment的记录
                cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%'")
                rows = cursor.fetchall()
                
                for key, value in rows:
                    try:
                        # 尝试解析JSON值
                        if value and value.startswith('{'):
                            parsed_value = json.loads(value)
                            
                            # 查找用户相关信息
                            if 'user' in key.lower():
                                if isinstance(parsed_value, dict):
                                    if 'email' in parsed_value:
                                        result['email'] = parsed_value['email']
                                        result['logged_in'] = True
                                    if 'username' in parsed_value or 'name' in parsed_value:
                                        result['username'] = parsed_value.get('username', parsed_value.get('name', ''))
                            
                            # 查找登录时间
                            elif 'login' in key.lower() or 'session' in key.lower():
                                if 'timestamp' in parsed_value:
                                    try:
                                        timestamp = parsed_value['timestamp']
                                        if isinstance(timestamp, (int, float)):
                                            result['login_time'] = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                    except:
                                        pass
                        
                    except json.JSONDecodeError:
                        # 如果不是JSON，检查是否是邮箱
                        if value and '@' in value and '.' in value:
                            result['email'] = value
                            result['logged_in'] = True
                
                conn.close()
                
            except sqlite3.Error:
                pass
    
    except Exception as e:
        result['error'] = str(e)
    
    return result


def get_workspace_account_info() -> Dict:
    """
    从工作区存储中获取账号信息
    
    Returns:
        dict: 工作区中的账号信息
    """
    result = {
        'workspace_accounts': [],
        'recent_projects': [],
        'augment_workspaces': []
    }
    
    try:
        workspace_path = get_workspace_storage_path()
        if not os.path.exists(workspace_path):
            return result
        
        # 遍历工作区存储目录
        for item in os.listdir(workspace_path):
            item_path = os.path.join(workspace_path, item)
            if os.path.isdir(item_path):
                # 查找状态文件
                state_file = os.path.join(item_path, 'state.vscdb')
                if os.path.exists(state_file):
                    try:
                        conn = sqlite3.connect(state_file)
                        cursor = conn.cursor()
                        
                        # 查找用户相关信息
                        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%user%' OR key LIKE '%account%' OR key LIKE '%augment%'")
                        rows = cursor.fetchall()
                        
                        workspace_info = {
                            'workspace_id': item,
                            'accounts': [],
                            'augment_data': []
                        }
                        
                        for key, value in rows:
                            if 'augment' in key.lower():
                                workspace_info['augment_data'].append({
                                    'key': key,
                                    'value': value[:100] + '...' if len(str(value)) > 100 else value
                                })
                            elif '@' in str(value):
                                workspace_info['accounts'].append({
                                    'key': key,
                                    'email': value
                                })
                        
                        if workspace_info['accounts'] or workspace_info['augment_data']:
                            result['workspace_accounts'].append(workspace_info)
                        
                        conn.close()
                        
                    except sqlite3.Error:
                        continue
    
    except (OSError, PermissionError):
        pass
    
    return result


def get_all_account_info() -> Dict:
    """
    获取所有账号信息的汇总
    
    Returns:
        dict: 完整的账号信息汇总
    """
    result = {
        'vscode_accounts': get_vscode_accounts(),
        'augment_account': get_augment_account_info(),
        'workspace_info': get_workspace_account_info(),
        'summary': {
            'has_augment_login': False,
            'primary_email': '',
            'account_count': 0,
            'last_activity': ''
        }
    }
    
    # 生成汇总信息
    augment_info = result['augment_account']
    if augment_info['logged_in'] and augment_info['email']:
        result['summary']['has_augment_login'] = True
        result['summary']['primary_email'] = augment_info['email']
    
    # 统计账号数量
    account_count = 0
    account_count += len(result['vscode_accounts']['github_accounts'])
    account_count += len(result['vscode_accounts']['microsoft_accounts'])
    if augment_info['logged_in']:
        account_count += 1
    
    result['summary']['account_count'] = account_count
    
    # 获取最后活动时间
    if augment_info['login_time']:
        result['summary']['last_activity'] = augment_info['login_time']
    
    return result


def format_account_summary(account_info: Dict) -> str:
    """
    格式化账号信息为可读字符串
    
    Args:
        account_info: 账号信息字典
        
    Returns:
        str: 格式化的账号信息
    """
    lines = []
    
    # AugmentCode账号信息
    augment = account_info['augment_account']
    if augment['logged_in']:
        lines.append("🔐 AugmentCode 登录状态: ✅ 已登录")
        if augment['email']:
            lines.append(f"📧 登录邮箱: {augment['email']}")
        if augment['username']:
            lines.append(f"👤 用户名: {augment['username']}")
        if augment['login_time']:
            lines.append(f"🕒 登录时间: {augment['login_time']}")
        if augment['subscription_info']:
            lines.append(f"💳 订阅信息: {len(augment['subscription_info'])} 项")
    else:
        lines.append("🔐 AugmentCode 登录状态: ❌ 未登录")
    
    # VS Code账号信息
    vscode = account_info['vscode_accounts']
    if vscode['github_accounts']:
        lines.append(f"\n🐙 GitHub 账号: {len(vscode['github_accounts'])} 个")
        for acc in vscode['github_accounts'][:3]:  # 只显示前3个
            if acc.get('email'):
                lines.append(f"  - {acc['email']}")
    
    if vscode['microsoft_accounts']:
        lines.append(f"\n🏢 Microsoft 账号: {len(vscode['microsoft_accounts'])} 个")
        for acc in vscode['microsoft_accounts'][:3]:  # 只显示前3个
            if acc.get('email'):
                lines.append(f"  - {acc['email']}")
    
    # 汇总信息
    summary = account_info['summary']
    lines.append(f"\n📊 账号汇总:")
    lines.append(f"  - 总账号数: {summary['account_count']}")
    lines.append(f"  - AugmentCode 登录: {'是' if summary['has_augment_login'] else '否'}")
    if summary['primary_email']:
        lines.append(f"  - 主要邮箱: {summary['primary_email']}")
    
    return "\n".join(lines)


if __name__ == "__main__":
    # 测试功能
    print("🔍 检查账号信息...")
    account_info = get_all_account_info()
    print(format_account_summary(account_info))
